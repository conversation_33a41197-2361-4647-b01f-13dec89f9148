import { Api, client, TelegramClient } from "telegram";
import { StringSession } from "telegram/sessions/StringSession.js";
import { NewMessage, NewMessageEvent } from "telegram/events/NewMessage.js";
import { Bot, Context } from "grammy";
import PQueue from "p-queue";
import CONFIG from "../config.js";

interface SendMessageTask {
  chat_id: string;
  text: string;
  parse_mode?: string;
  disable_web_page_preview?: boolean;
  reply_markup: string;
}

interface ForwardMessageTask {
  chat_id: string;
  from_chat_id: string;
  message_id: string;
}

// 每个 bot 的独立队列
const botQueues: Record<string, PQueue> = {};
// 初始化所有 bot 队列
CONFIG.BOT_TOKENS.forEach(item => {
  botQueues[item.token] = new PQueue({
    concurrency: 1,           // 每个 bot 同时只发一条
    intervalCap: 20,          // 每个 interval 时间段最多执行 20 次
    interval: 60_000,         // 以 1 分钟为限流单位
    carryoverConcurrencyCount: false, //  不允许上一个 interval 剩余的额度顺延
  });
});


// 转发消息队列 每秒转发一条
const forwardMessageQueue = new PQueue({
  concurrency: 1,           // 并发 1
  intervalCap: 1,          // 每个 interval 时间段最多执行 1 次
  interval: 1_000,         // 以 1 秒钟为限流单位
  carryoverConcurrencyCount: false, //  不允许上一个 interval 剩余的额度顺延
});

// 缓存用户信息
const userCache = new Map<number, Api.User | Api.UserEmpty>();

// 添加一个缓存来记录用户的最后转发时间
const userLastForwardTime = new Map<number, number>(); // key: 用户ID, value: 时间戳

// 已连接成功的账号
const phones:string[] = []
const botList: Bot[] = []

export async function startTelegramClient(phone: string, sessionString: string) {
  const client = new TelegramClient(
    new StringSession(sessionString),
    Number(CONFIG.API_ID),
    CONFIG.API_HASH!,
    {
      connectionRetries: 5,
      floodSleepThreshold: 237,
    },
  );
  try {
    await client.connect();
    const me = await client.getMe();
    phones.push(phone)
    const keyList = Object.keys(CONFIG.USER_SESSIONS)
    const idx = keyList.indexOf(phone)
    if (idx === keyList.length -1) {
      const txt = `🤖重启通知 \n${phones.join('\n')}\n以上${phones.length}个账号已成功连接,监听准备就绪`
      botList[0]?.api?.sendMessage(CONFIG.TARGET_CHAT_ID, txt)
    }
    console.log(`\x1b[36m 📡 Telegram client 👤【${me.username}】 connected and listening for messages \x1b[0m`);
    // 获取所有对话
    const dialogs = await client.getDialogs({
      limit: 100,
    })

    // 过滤出群聊和频道
    // const chats = dialogs.filter(dialog => 
    //   dialog.entity && (dialog.entity.className === 'Chat' || 
    //   (dialog.entity.className === 'Channel' && dialog.entity.megagroup === true))
    // )
    // console.log('[ chats ] >', dialogs.map(chat => [chat.title, chat.id?.toString()]))
    client.addEventHandler((event) => handleMessage(event, client, me), new NewMessage());
    // 根据配置来给 bot 发送消息
    // CONFIG.BOT_TOKENS.forEach(async (botConfig) => {
    //   const msg = await client.sendMessage(botConfig.username, {
    //     message: `/start`
    //   })
    //   if (msg) {
    //     console.log(`账号已先给 bot【${botConfig.username}】发送消息`)
    //   }
    // })
    return me
  } catch (error: any) {
    console.log('[ 连接错误] >', phone, error.message, sessionString)
  }
}

export const ACTIVE_HOURS = {
  start: 17,
  end: 3
};

async function handleMessage(event: NewMessageEvent, client: TelegramClient, me: Api.User) {
  // 1. 获取北京时间
  const now = new Date();
  const beijingTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Shanghai" }));
  const hour = beijingTime.getHours();
  // 2. 时间判断逻辑
  const isWorkingHour = (hour >= ACTIVE_HOURS.start && hour <= 23) || (hour >= 0 && hour < ACTIVE_HOURS.end);

  if (!isWorkingHour) {
    return;
  }

  const message = event.message;
  const chat = await message.getChat();
  const sender = await message.getSender();
  if (!chat || !sender) return;
  if (chat.className === 'Channel') {
    // 获取发送者信息
    const msg = message.message
    const matchedKeywords = CONFIG.KEY_WORDS.filter(keyword =>
      msg.toLowerCase()?.includes(keyword.toLowerCase())
    );

    if (sender.className === 'User' && !sender.bot && !!matchedKeywords.length && msg.length <= 10) {
      const _chatUsername = chat.usernames?.find(username => username.active)?.username
      const chatUsername = chat.username ?? _chatUsername ?? null

      // 检查用户是否在2小时内已经转发过消息
      const senderId = Number(sender.id);
      const lastForwardTime = userLastForwardTime.get(senderId);
      const currentTime = Date.now();
      if (lastForwardTime && (currentTime - lastForwardTime) < 2 * 60 * 60 * 1000) {
        console.log(`[ ⚠️ 已忽略 ] 用户 ${senderId} 在2小时内已转发过消息`);
        return;
      }

      // 更新用户的最后转发时间
      userLastForwardTime.set(senderId, currentTime);

      // 如果是简略数据 并且没有缓存过 则获取详细信息并缓存其值
      if (sender.min && !userCache.has(Number(sender.id))) {
        try {
          const result = await client.invoke(
            new Api.messages.GetHistory({
              peer: chat,
              offsetId: 0,
              offsetDate: 0,
              addOffset: 0,
              limit: 50,
              maxId: 0,
              minId: 0,
            })
          )
          if ('users' in result && result.users.length) {
            result.users.forEach((user) => {
              userCache.set(Number(user.id), user)
            })
          }
        } catch (error: any) {
          console.log('[ 👾 获取消息历史失败 ] >', error?.message)
        }
      }
      const cacheUser = userCache.get(Number(sender.id)) as Api.User | null
      const senderUsername = cacheUser?.username ?? sender.username ?? null

      // 如果是简略数据 并且允许转发 则转发到 bot 并且再转发到群里
      if (!sender.username && !chat.noforwards) {
        const taskFn = async () => {
          const peer = await client.getInputEntity(message.peerId)
          return await client.forwardMessages(pickAvailableBot().id, {
            fromPeer: peer,
            messages: [message.id]
          })
        }
        enqueueForwardMessage(taskFn)
      }

      const replyMessage = `
用户昵称：<b>${sender.firstName} ${sender.lastName ? sender.lastName : ''}</b>
用户名: ${senderUsername ? '@' + senderUsername : '-'}
来源: ${chatUsername ? `<a href="https://t.me/${chatUsername}">${chat.title}</a>` : chat.title}
关键词: ${matchedKeywords.join(', ')}

发送内容: <b>${message.message}</b>

时间: ${new Date(message.date * 1000).toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      })}
`;
      // 构造 reply_markup
      let replyMarkup = {
        inline_keyboard: [] as any
      };

      if (chatUsername) {
        replyMarkup.inline_keyboard.push([
          {
            text: "🔍 查看原消息",
            url: `https://t.me/${chatUsername}/${message.id.toString()}`
          }
        ])
      }
      if (senderUsername) {
        const userButton = [{
          text: "💬 私聊用户",
          url: `https://t.me/${senderUsername}`
        }]
        replyMarkup.inline_keyboard.push(userButton)
      }

      enqueueMessage({
        chat_id: CONFIG.TARGET_CHAT_ID,
        text: replyMessage,
        parse_mode: 'HTML',
        disable_web_page_preview: true,
        reply_markup: JSON.stringify(replyMarkup)
      })
    }
  }
}

export function enqueueMessage(task: SendMessageTask) {
  const { token } = pickAvailableBot();

  botQueues[token].add(async () => {
    try {
      const response = await fetch(`https://api.telegram.org/bot${token}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(task),
      });

      const result = await response.json();
      if (!result.ok) {
        console.error('Telegram Bot API 错误:', result);
      }
    } catch (error) {
      console.error(`bot ${token} 发送失败:`, error);
    }
  });
}
// 转发消息队列 每秒转发一条
export function enqueueForwardMessage(taskFn: Function) {
  forwardMessageQueue.add(async () => {
    try {
      await taskFn();
      return true
    } catch (error: any) {
      console.error(`转发给bot 消息失败:`, error.message);
      return false
    }
  });
}

// 简单 round-robin 获取可用 bot
let lastBotIndex = 0;
function pickAvailableBot(): { username: string; token: string; id: string } {
  const tokens = CONFIG.BOT_TOKENS;
  const botConfig = tokens[lastBotIndex % tokens.length];
  lastBotIndex++;
  return botConfig;
}


const handleBotForwardMessage = async (ctx: Context) => {
  // 被保护的信息不转发
  if (ctx.message?.has_protected_content) return

  if (CONFIG.TARGET_CHAT_ID.includes(String(ctx.chat?.id))) return
  // 忽略以斜杠开头的命令消息
  const messageText = ctx?.message?.text;
  if (messageText?.startsWith('/')) return
  try {
    await ctx.forwardMessage(CONFIG.TARGET_CHAT_ID)
  } catch (error: any) {
    console.log(`[ bot转发用户消息失败 ] >`, error.message)
  }
}


try {
  CONFIG.BOT_TOKENS.forEach((item) => {
    const bot = new Bot(item.token);
    // if (item.token === '7055170525:AAGERJzFnnPxgavGRfdGE6d-SaGVgPjbr3o') {
    //   bot.api.getChat('@yiluchanghong929397').then(res => {
    //     console.log(111, res)
    //   })
    // }
    bot.on("message:text", handleBotForwardMessage)
    bot.start()
    botList.push(bot)
  })
} catch (error: any) {
  console.error('启动 bot 失败:', error.message);

}