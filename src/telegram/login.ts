import { TelegramClient } from "telegram";
import { StringSession } from "telegram/sessions/StringSession.js";
import input from "input";

const apiId = 22241761;
const apiHash = "afe88495d0832f0f6e522ef325e5637f";
const stringSession = new StringSession("");
(async () => {
  console.log("Loading interactive example...");
  const client = new TelegramClient(stringSession, apiId, apiHash, {
    connectionRetries: 5,
  });
  await client.start({
    phoneNumber: async () => await input.text("Please enter your number: "),
    password: async () => await input.text("Please enter your password: "),
    phoneCode: async () =>
      await input.text("Please enter the code you received: "),
    onError: (err) => console.log(err),
  });
  console.log("You should now be connected.");
  const session = client.session.save();
  console.log(session); // Save this string to avoid logging in again
  await client.sendMessage("me", { message: session + '\n session' });
  process.exit();
})();