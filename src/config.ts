import * as dotenv from 'dotenv';
dotenv.config();

interface IConfig {
  /**端口 */
  PORT: number;
  API_ID: string;
  API_HASH: string;
  USER_SESSIONS: Record<string, string>;
  KEY_WORDS: string[];
  BOT_TOKENS: { username: string; token: string; id: string }[];
  /**目标聊天 ID */
  TARGET_CHAT_ID: string;
}

const CONFIG: IConfig = {
  PORT: parseInt(process.env.PORT!),
  API_ID: process.env.API_ID!,
  API_HASH: process.env.API_HASH!,
  // USER_SESSIONS: {
  //   "0410": '1BQANOTEuMTA4LjU2LjExNQG7PJBD6VTZAt6wRoEum00QhUeCUuMDmlG5FTW5NCWbC1QuQjdALmDo0BY75TSMQGq7acuDqnM1Ftq4an5j1YLrdwgLWQLxs7bOTVHuP2mccQzEksP3w25NQZnfVy8L+yW7f7KCPPJKFGItuUo3TLpUUeWmDwJb4Bx0DMbD6HtkBfx6Di6Wzfky2niK/BWXEJ/qKKBY8aCAzUDGpJV7kK4Nwnxqp0FtEzeWinB1lS3R/WLsOKMGSfKUpU7almlBIiAVBESBBS0L2642RH/WwYzXs0Gid++J0/rsDuOZ5JhU/ko2jSrGxLHFrBuIKtRagw7+0vXjxGH00UVj1ZLxH6yE+g=='
  //   // "905011141095": "1BJWap1sBu2QsigIuHFgSYojua8YLndPY7Deh97qRDbPQ_U0CdyjxJqSTfUanDLSDk-XuzwXv_ZbPkhgcExdxf5aeFoM_NX9G2_ZrBfMz8vjmWNP-zsvWFI1Wl2FyzcgEUoF8V1OFP3-dXvozTVOkonBqgVerss3Le1GaIXh2umpTUM8WITVOAqkwOVdpPyNi_RxN1kNmqBoxJuApLF800BY4YDJPyLt1oPAGpFM1lPQhKWMPGFgaxzSyDyMdkRUdg1mBz0Yl9y_1peWRNvPHa_zIN6XPAsaikdQ5ZPvPktMI0Z036VzIdlYbKedYKWXDVFrFabfcMULf4VoagOANpfT6UJSURwo="
  // },
  USER_SESSIONS: {
    "905011141095": "1BJWap1sBu2QsigIuHFgSYojua8YLndPY7Deh97qRDbPQ_U0CdyjxJqSTfUanDLSDk-XuzwXv_ZbPkhgcExdxf5aeFoM_NX9G2_ZrBfMz8vjmWNP-zsvWFI1Wl2FyzcgEUoF8V1OFP3-dXvozTVOkonBqgVerss3Le1GaIXh2umpTUM8WITVOAqkwOVdpPyNi_RxN1kNmqBoxJuApLF800BY4YDJPyLt1oPAGpFM1lPQhKWMPGFgaxzSyDyMdkRUdg1mBz0Yl9y_1peWRNvPHa_zIN6XPAsaikdQ5ZPvPktMI0Z036VzIdlYbKedYKWXDVFrFabfcMULf4VoagOANpfT6UJSURwo=",
    "905061615623": "1BJWap1sBuz3tQ115nLvj00bpGjB1TY-64Hes17JrN8v4ZM6_MJ3u-Mvrb9gr91Ntcnd9NGPseCdQ1p8DhhZBs6g2nHb4om5oBUn7ABywzB_btyieMmMK5fhk2unVLOc_-u2WCperRSrw0q0ODApIPYLT04Bve_CU1Y6kolkqjz8XL1mXc5jpNekpn_YKqGpbqgFNpHEjpDJQo9TI0eBKuJUl8-zzFSLsBJPT1u6DeyDzseY-apNeKRqJfZWRFXSSBstDc_DkVsLakjx3Y2cPzJD3NDaEKpWvWrrbq5aiI-iDPG8Hz52swXbM3xi0y7V75XhEIMHzjLAYsKZJ58zqRQRMc2aNu_s=",
    "905014768738": "1BJWap1sBuxgU4kflf92pxdyeWAxPgGHr5MPWdPXzCu18aoatn5GGcV-C34S7PPreM661ivODLo_UzJ8HZU8Um2BfYsrqMBrDkCESZkc3BG3NajT9gBMvGGXJ1L_l7Yh34lBMiV79Y9HQKDo7gyp73npB6STovT8A_xdr_JQKOEn7fpJ3E4-A2V1FXKKhXE_3QBNM7OSvY_CrCVc7SvCToB5wm7HLZQ4Hib7hOF8lhNa4fkEQOQegxy17cO7_iaoE7MIPlQoKBkyli4IOmqy7T9g9G8AwPkNSLAEYfHUSUhQus_itfIuIqiMduT5b_RgBiJa-1XGloRYIFI6z3RRcHgAEXL8LC-A=",
    "905315421756": "1BJWap1sBu1xRMuIT1DTaJobryJKf0-lQA9g8VRHL9TJVcr6or7zpTqy0jo_gVUhZVxOXkFGjkRBB4ZCyp1ow5K9Or_hFFJiyM6rj1wGQV018Qk7X30v6CSGROHXaT2S50Xxqc9eFfjvNnTFsNK_iKbn-B-pMJoelXDPT66nywA7cCLYdTrD7JQmO-8TPgwF2egB-Fz6lW5tK81LYvGQ639mbBid0qYS9OIQ0sNDtNsOSz8Q-2Q9XOltxv0KqbUS1JPPWUizvdIPoWG4r-BuI50GpGmkbgcTRsda-puORrjKKpqp_Ozh8n15px_nFSk4tLcwCWlMmEf9Ad-rrVUHLUwGJmP5CWNA=",
    "905334463580": "1BJWap1sBu5Wip86znhQeZYGxDOZN4th6zuggQ_0uvc6SaTL4rJkU3QMBO3obqaU3KMhBNOk1hOnVsT8xusonUlVC2m_n3mgYRv9ozZbGPkqVnvm8W9B8xzsqntI34bcd1R7Lev_GpGWuvNWgdhdOH0Up_O0ha_NUe-H8sAy_-LS198--L11sTHTKFp7AMxX782qajiED6V4J7bJt6QmyHlGXK4I2d_foE2FkWVb1M01ooGKSBCEkYlKTjW-eDc38RYx7-zZ3C4WlWp8Zbbe_pSoCnTvpfsgpJN2mjQhfo3fVMQfCwNdih-dIARiPTxGL-952us6F86H_jJOGvHUsBRKB8fF4IxE=",
    "905067259031": "1BJWap1sBu259gTfhohi7YH-GIOq3wPDm6aOkLP6zW8prHTtpWpLmZGMAVMTWAbXznxi6_zf4BY5lwi3ZPMKu9JxhjdhjPxTQ-A_6LfxA0EM33UN4ks--tlEcgtoQ6Ijw7DqIkSlfftdFAmQzYHxyDxzrv3i5RtVx3S58OxM7ycrjtgwj8vSZXTYP6lJqIJpwaMsgsJh1g4N4XN3fM4O2dw_89H3_RRRSZKIqGNIyOJHSrjth2anjJR_u3hprTuEJmhupISpuXRe9G-2fc1hF-Zn86DkE5DdCSktzBAZhDcT2xmgeq7oogm-PEg_1T6mvmZsQDpDQebqQcfdweRFTa7H7dkBziGE=",
    "905513035448": "1BJWap1sBu2AT0FIiDGQhW-VOsZ1d9gomHwn0fhoajqYtMJgYQX-e55OvEzYrTjRI-roMx9b1qU5_U7hu27sptm5HnTwaXyvbdjl8Hd1z9z1GaTFe856DAaxUlEVoGNUvlmuxhdD08z_s6KzCRPaAstEm3IeCDqDxvG_S1E_o2tY2MIQwLupNYTOpkF8ZqIFv__Z1B3yk8-htzbBvQnBMYovXzFNMtjCY39Y710ySkNX5tf8NROHDnnpVdYBekvPGxX2g6Gg0_7vBqF-3xekfwfycXTSabK4numKgAiZio1ylbMUzCHe19I5VmDlequ9Fy2HoduFVNOuzu9AP-h3XLib5WzlYDNw=",
    "905069005631": "1BJWap1sBuyYvGPLv3SE8mUHpKsR2Wa-qDEtXdaetxncGotd-eafrgDPL5BmYXJZ90OypzBAAT_6X0Nd2s8LLQS-OSWDkTMxC5k6DUj33B4WBraBfgUsI7dFoYZNvBEfHZqE4rU0PY7EZZLxXJ2f5_CVtgHGRclTK5CBbPaCwAODhDd4I2yelrBudZHP37apzpB49zwNzv8HbCXpTJF4ZcbVoKLP-b0F0xpJKbACd0v-nlr4BiQdJotuDOi-j6YE0lDY_sZ45AumFCM989Z33HXtcPKXYBHb7YLiT1N5Qh4LDNZP3gW7CrQST331HUEHiynzkYkzKyeOQQmepXw4ZBPwX5VuDevs=",
    "905487812305": "1BJWap1sBuy_rq7MMryddbGj1uUx3aJIv7xnTtxhC1szXCGUrAxHO5MCrjmKchLM82LMM1ljykOURGKuHUM2jd3I-YSIUHXb5va35ua9QAv_jFt0LaG0TKkYErZYQDo1xT3r_vOH0hFaoKERU2lHsIStu1uiX8tJbsuJFQvr7EwdCBQiwIfKO5H5oURi0pbjjsD7lFmiVW4aviHdE5hjT0ELdghljwo6Ivl4sgTyrbHayWjFS_Z6mBaXgLnoyn6Xbhnnat65q75chwGdkSb9P-k6PDD9kC2EDdJ6tnN7Y0AeR8QMb334vSuZgktkdKfH2XX_Pf1qszItemHVARHxBAkJ_Br23LNA=",
    "905015144510": "1BJWap1sBu5_J4TPxWXXpSaW9eRG3r8SnweCVuxZcCOVKXroZT8cRxQ2cMyHDi98oeiOCgbu0pN3K5t4zjAz_U3z_CPGBOSgcZNUjcf_6dvZEIvu_CuhKbE9CY94SSUdl0VPpE0nPmRnd99MUfgwgdJiKkqurvPOpGN2GZOpqCHjRTIL5eE1hlJ2-RFuPwZ49FTKMXehI0ELnObYHGK3AwiBhyh-zdY0gpA4LxpwBzmSEp_XyD42lwkgwn4elXf8z-KxkCN9rZNsXPcNZ07NL7TKUNxDoa1s274iNCE4zmqo-9gGi7dtXpGzaObT5XDDGpbXPzpg4Uz-9gti4QOdSHy_0sS4nr4Q=",
    "905017372241": "1BJWap1sBuwmml86R-xhdbe2s5of4Syyp7Aqw-iYTFmVxKBlvIWslg0RCVxlr9aMSUGgswBRsdtDgaDjzRygDmTY68ly6AvuOqDizWEEV0_YBQgz3RA8oB5jiJ65CIrOMb8A1toyeGu8CbwSJi0xfrUgjBYbYQwgtk106FdJ88GhvA0HmIZ6zwGfBDWlmIe1zrt5w2zXc42TRBKZYDuK4Lp9UH6Tzd2udapGUNLtO7z4UN1rFvQ9SgfMQCVxGh3pVz18fdEoGxN7fmD4Q8swJC_cdZ_Rfw3zMVANDkSLXW0A5zJcKb9v1BHKXzjCvco-L480yttsfEBenUWs5ciWZSE3xLlOpieY=",
    "905387049901": "1BJWap1sBuwuBY2i1N5ipAuKFrJV-tcOtoY5V5jR5xaV7gXkg3_ca5aAJwTegS8JPZ-QGMdVurCHE_UhdT4GNX0Re6JFVAcTOg5_LUgKaAWZ0IFM_1qnzllQiSQIW8hzV_14cTaZsctwmVv57pYoYkWUVVB4n_qrd-QXyhuywizfIywZaNItdWycl-VExBGTB_Qrr4jFVEHur0hOsUlQ5BsQSyS-RtNFRr0Sp1VI3wu-OVJl4tpwF1t31f9nT_SU_5IerHVt7kvgy2K_fEY0kPP_q1KGopY_rc15_OWKPeVkQeWRu0m59qQYGau37DYZgLAYq1qqlOX58dB7Sl5YTn6gBnUX-P7k=",
    "905457163843": "1BJWap1sBu6z-LSuCBQFSIJLCbF6M7EpoMx1uDK9603W632rXyKhk8ik9Ul2XLd1U5LIdlyjYWDtr-mBFBuBOkap07uatEnGJ75aKPpidDt1vpcjOz9cYoAS9l92CvxNImniKL149gH89_HU-uwu-vHovpDcfbFlPOZwtDG2deKBoBCb3M6lgCIB_f7pQ0cU31x9ZnJPWu1Zq13dQvlz4DaopQLJMexd-Wyfcn9ZeBl0U9ukpyPSMUUkCDBDniU2bfqfAMKzSX9T_8wkCOj4ZgwS-k0KmQ96USxk_-qmg0gXihfyaEMOtRapDWrY2nfHfyYa8zp3fP__L_sV7J3YBzNgTwNGimWM=",
    "905447558864": "1BJWap1sBu7FP1aTAxsuSvGtK7e1c3ep8xBnpA3AvXQQrvur71whM_AAPk06KfpKVNKwGwLrpKzqUsnTGDkOxS59JAi2e0nZvGY5h_1UNQnnrYu8IGWfgLjmMKGPNunR2ZpkugULtGKb06PxdLhHuFAbyxDxYFP8DwyHbqtisRbKh9cBCdi8fgxzGhxCXGgzMkI4u3TsyqMPPLgPeOfo0mwaIT_heNB92h_VX3KWUflO69Dm7NGkwqJ42xMYyn6RTGULdj6sXegw-NxjoZAIZ874SQ9JhtausiIEVf-VKrrabhffM8_PzbpP6ZgprMDbo56J1_wB8oZmKwT9VVCHqYq56-zGGpf0=",
    "905453626631": "1BJWap1sBu4BYUzmzPwW9NTPvwlypMpP6gJiPti5z80mbdWGd3XE0w1vW0kcc-QhV2tcP4z_erMHwX7GqtN0PQd-42WyBID9mlMcwnJ6F9L45-ZwZLv-tIi6NzXXx4TFyhJFuolW2asHnkUoDnqA__01HngR2bt0PHvic5suPOFH2VInFaY9IpMaxbka7gc5B3T5rBPoVpVnPsau5WQRShz92iFFlXwdTdQq5IId-MEeLgQFveYxeunuQLKDuRoDyGy_ohY9CRC6uojIgAvuCx5VPZpakj3IGa6Cf21HRtst6qN1YhKovzfpE47TdFfFuewXW2-uhCklVLNCdRwIlM4jMFqp_8bQ=",
    "905441845646": "1BJWap1sBuzydUcpkDXz_Z-_knVjdqycTP8JLGNFLP3R9ogsE0Oc-trYpfHDvIAHEjWiZCQ0te2Hu3a418Wx3B9rtQ8BFW-xk56zOPttMopi-oiGqUxVP9WnDQiHCmGx9p7DdzlQ7Vi3oaxrcs0PPcs_vJ7gvYAIny5yRr-T1lFUBrShgb8dAeKpMIvEHlCEQ67zhFEnGRQMriyCVt2cpx6_2sXazfnZ8h400UnJNEU9n_ghSQWTq_-EeeZPURvBAbWTRZEwsZ_j4jdhX3DpC6wXXB5dprtoiCNOFgnEUrdkj35ar_s5QON_ybSwa2fiKlFslF6ecvGelYgEZeOJulLUuYLbaPFY=",
    "905341106570": "1BJWap1sBu4WhNjdpuHd5MbZPFLoeGbv9xhoHplGtOwI9zEcPoxgq4ZIgaOkzhaRG5CGHDOEsuuZ5pBmb-KzdWf2wrVgQ_0CP03IvPH0DAh9VIHGE9mr6pstk8igTnWU3C4ce6-6XyrYiM8a_JmRMKsLJrMW4kccJx399tpaR2wm8GtNPRc1eztU0dRMrOyyEPswy-y09v_lp7Yrf-Br9YYIXomrxEz7HtLg95hRxdB3j7smpBhB438ef9gACtrKVYCdePNRM1w08TIetFi7nWq1u1zFlkM4vG5OofkYw0-cVSa5lJNf1AVd7t_15yyJ0JpWPeyR5XD-NlmLAHfrakOBG8sRASnk=",
    "905489593792": "1BJWap1sBuy5QDCX72U8bp_N4hRGanM-98G-CYv3cFT5KbfzBeWWhIHBT9jmjrR1gBR35k5FN0SYgXvOiIDHO2fZZyc_ZqyosEnJnDDCzEm87soBejcoUQ7g-r2DDp1rbItXuH7C6Q9BaMjLFR_gzlgkHHmEmAO2DM0zpVIxDwvzGg0ImRyhioOi0-IEz5pJbMJIgoSFoANjavSWjzf33anuhW1VQzIdBnJFSbTyrPPPKcKshSrOZHz-YXghqKlBO4Nbva_D7XSC1q3XPZBADsAWeWntpa8-UDYoVFu5BJFAVjWHOHW3myJigSg7KiVw0PpILsOOy0BDANrJ_UQnpJotD_Pja3YY=",
    "905051736353": "1BJWap1sBu6NBjsrZLSuKyZKZlFWUajwOsH8I06Vnk1xDJBYgWOJoXtT-Ov95HhknYl_b20ryFFeSJHKxXtF16Cq8fmrBwuVXlb0UgI4QFI5D0b2AlD7olvEYUfshFxUQOC5wox7hYzFCMxHQpPmLiKivHPQf5ZyqaoANqWk_DOj6pSiqNRuMLRnHQC2pQtMKJA51wB4IgPl9gVl_5KxPPv2rVoipWQzYfNJJ60y_jN6z_bg5kcYYqVdhvhFfIIWRJtfJuV-uaCtopSq4x0-sVInmME_cbm50Tjz8bNcjpAxBeER3xg1fq8J2_hsHEmpAZvgFcuVxRxBJ5iY3CyKELmI-f2epNyg=",
    "905432279120": "1BJWap1sBu2MNyq1agoTDv7uqwrmiDOdweqy1g4gSaRkcnTCkN2S59gZmi63L4KVpAZgXNEqmuXNFxZgafK9aiacoQ3XQIT5_WQPaYvZEBZRIPerG2t8Y1TddU7N9QayRv5i_uc7GutWvSe6i33qvPo9l6P1-TdGw33j4Z3qOpa6HHXg-wYelPAUZjyv_G3un_6Zdhg00Co9RACS80HeApwZ7CX4tBDl6v8u7KuQpy5NWYDOpUBl17TcjsyJ0uB5zQzHCdGS0B0Q7-6dRtqYGW4Y_f5G4MvZ4eYfcyfzzMqE9IPaWYl0qt8QoFvmZkRMQ0AlpqPvpmMIB-oLewJrH63ZFor0CFGI=",
    "905077831851": "1BJWap1sBu4vtvmcebfHCcjCxY68BCe0DqvMjYey1Vv0ornaTp8V4ND-pUgqnXnOp_sXgwtYHs6B7uud4ZI1EnwySB_SiMFpE-i1FV2nSUkRuaF8hNMXJ5987Pi3tqKRFvbNuX6l5qSISFmY8bGFYmSeNmOPEDEJ4lOsxeED9LVRRCIW-NGAda_xR6kdcBE6F9nzSQzFr--287ccgX9TFs78g9YSiw9zlRT1voxTVhn74WLQDcgTfyduoyS_N9i8XkuiGSGYkusPBIr3Cr9ec_KH7TDZowoBjdcTEpbnyj1cccza3_oveLLU8YMouE9e4v9EcjMRrWsl0EaFV3uiBLu5HBu8ToAc=",
    "905482763226": "1BJWap1sBuzc-jbBs_23HP0YLT4LkNp_bcGTd75Mogtgp4p1tg1mJh4oWsC4OW0Q4YMU_Y21tOrSxfxtwgVG2lqqgyyxikV9M7fzR2MDYvM1h1JmGMF8UfF6LALVctazQRjIYMzVoAlioBbmP2KcE90Z2_xOIKO_4JCtyo31Psfwn5Cw1uagnY3BJOLqyTzmHGkltAc8PMQ2xPlhtzg8TC35tPc0c5N7bKLxZO7EL2h6b2eIad-YE0EmSns3OLzSnjiVEbMAKyOgQP3VAXAFW37RDu-9xn3bTzmD15de-6fGgVV4V7jiEHvoF9RxT8Wp36tw2nvGWdaD2VF-lPvpDaQzKpHP7CoM="
  },
  KEY_WORDS: [
    "有吗",
    "有么",
    "有没有",
    "万达",
    "吾悦广场",
    "有嘛",
    "上海",
    "黄浦",
    "徐汇",
    "长宁",
    "静安",
    "普陀",
    "虹口",
    "杨浦",
    "浦东",
    "闵行",
    "宝山",
    "金山",
    "松江",
    "嘉定",
    "青浦",
    "深圳",
    "福田",
    "罗湖",
    "盐田",
    "南山",
    "宝安",
    "龙岗",
    "龙华",
    "坪山",
    "光明",
    "北京",
    "东城",
    "西城",
    "朝阳",
    "丰台",
    "石景山",
    "海淀",
    "房山",
    "回龙观",
    "顺义",
    "昌平",
    "青岛",
    "市南",
    "市北",
    "黄岛",
    "崂山",
    "李沧",
    "城阳",
    "即墨",
    "胶州",
    "昆山",
    "东莞",
    "石龙",
    "石排",
    "茶山",
    "企石",
    "桥头",
    "东坑",
    "横沥",
    "常平",
    "虎门",
    "长安",
    "沙田",
    "厚街",
    "寮步",
    "大岭山",
    "大朗",
    "黄江",
    "樟木头",
    "谢岗",
    "塘厦",
    "清溪",
    "凤岗",
    "麻涌",
    "中堂",
    "高埗",
    "重庆",
    "渝中",
    "大渡口",
    "江北",
    "沙坪坝",
    "九龙坡",
    "南岸",
    "北碚",
    "渝北",
    "巴南",
    "武汉",
    "江岸",
    "汉口",
    "硚口",
    "汉阳",
    "武昌",
    "青山",
    "洪山",
    "蔡甸",
    "光谷",
    "盘龙城",
    "长沙",
    "芙蓉",
    "天心",
    "岳麓",
    "开福",
    "雨花",
    "望城",
    "长沙县",
    "五一广场",
    "德思勤",
    "南京",
    "玄武",
    "秦淮",
    "建邺",
    "鼓楼",
    "栖霞",
    "雨花台",
    "江宁",
    "浦口",
    "六合",
    "溧水",
    "高淳",
    "淮安",
    "清江浦",
    "淮阴",
    "银川",
    "兴庆",
    "金凤",
    "西夏",
    "海口",
    "秀英",
    "龙华",
    "琼山",
    "美兰",
    "老城",
    "杭州",
    "上城",
    "拱墅",
    "西湖",
    "滨江",
    "萧山",
    "余杭",
    "临平",
    "钱塘",
    "富阳",
    "无锡",
    "锡山",
    "梁溪",
    "惠山",
    "滨湖",
    "新吴",
    "常州",
    "金坛",
    "武进",
    "新北",
    "天宁",
    "钟楼",
    "江阴",
    "丽江",
    "潍坊",
    "潍城",
    "寒亭",
    "坊子",
    "奎文",
    "金华",
    "义乌",
    "榆林",
    "唐山",
    "太原",
    "小店",
    "迎泽",
    "杏花岭",
    "尖草坪",
    "万柏林",
    "合肥",
    "瑶海",
    "蜀山",
    "庐阳",
    "包河",
    "肥东",
    "肥西",
    "湖州",
    "吴兴",
    "南浔",
    "六安",
    "济南",
    "历下",
    "市中",
    "槐荫",
    "天桥",
    "历城",
    "长清",
    "西宁",
    "蚌埠",
    "滁州",
    "长春",
    "南关",
    "宽城",
    "二道",
    "沈阳",
    "和平",
    "沈河",
    "大东",
    "皇姑",
    "铁西",
    "浑南",
    "南昌",
    "红谷滩",
    "东湖",
    "西湖",
    "青云谱",
    "青山湖",
    "新建",
    "南昌县",
    "天津",
    "河东",
    "河西",
    "南开",
    "河北",
    "红桥",
    "东丽",
    "西青",
    "津南",
    "北辰",
    "武清",
    "宝坻",
    "滨海",
    "静海",
    "成都",
    "锦江",
    "青羊",
    "金牛",
    "武侯",
    "成华",
    "龙泉驿",
    "新都",
    "温江",
    "双流",
    "郫都",
    "天府",
    "南宁",
    "兴宁",
    "江南",
    "青秀",
    "西乡塘",
    "武鸣",
    "良庆",
    "邕宁",
    "佛山",
    "南海",
    "顺德",
    "高明",
    "三水",
    "桂城",
    "里水",
    "九江",
    "丹灶",
    "西樵",
    "大沥",
    "狮山",
    "大良",
    "容桂",
    "陈村",
    "北滘",
    "龙江",
    "临沂",
    "烟台",
    "芝罘",
    "福山",
    "牟平",
    "莱山",
    "蓬莱",
    "贵阳",
    "云岩",
    "南明",
    "花溪",
    "乌当",
    "白云",
    "观山湖",
    "阜阳",
    "颍州",
    "颍东",
    "颍泉",
    "福州",
    "台江",
    "仓山",
    "晋安",
    "闽侯",
    "赣州",
    "章贡",
    "赣县",
    "莆田",
    "荔城",
    "城厢",
    "涵江",
    "惠阳",
    "淡水",
    "沙田",
    "陈江",
    "沥林",
    "水口",
    "横沥",
    "惠城",
    "昆明",
    "五华",
    "盘龙",
    "官渡",
    "西山",
    "呈贡",
    "南通",
    "崇川",
    "海门",
    "江门",
    "蓬江",
    "江海",
    "新会",
    "台山",
    "开平",
    "鹤山",
    "厦门",
    "思明",
    "湖里",
    "集美",
    "海沧",
    "同安",
    "翔安",
    "扬州",
    "邗江",
    "广陵",
    "江都",
    "郑州",
    "二七",
    "金水",
    "新郑",
    "中原",
    "管城",
    "郑东",
    "中牟县",
    "惠济",
    "温州",
    "鹿城",
    "龙湾",
    "瓯海",
    "洞头",
    "瑞安",
    "中山",
    "黄圃",
    "南头",
    "东凤",
    "阜沙",
    "小榄",
    "古镇",
    "横栏",
    "三角",
    "港口",
    "大涌",
    "沙溪",
    "三乡",
    "板芙",
    "神湾",
    "坦洲",
    "珠海",
    "井岸",
    "白蕉",
    "乾务",
    "五山",
    "斗门",
    "上横",
    "六乡",
    "蓬溪",
    "平沙",
    "红旗",
    "温岭",
    "太仓",
    "泉州",
    "鲤城",
    "丰泽",
    "晋江",
    "石狮",
    "柳州",
    "城中",
    "鱼峰",
    "柳南",
    "柳北",
    "柳江",
    "柳城",
    "鹿寨",
    "洛阳",
    "老城",
    "西工",
    "涧西",
    "洛龙",
    "孟津",
    "盐城",
    "大丰",
    "泰州",
    "海陵",
    "高港",
    "姜堰",
    "宁波",
    "海曙",
    "江北",
    "镇海",
    "北仑",
    "鄞州",
    "湛江",
    "赤坎",
    "霞山",
    "坡头",
    "泸州",
    "江阳",
    "纳溪",
    "龙马潭",
    "嘉兴",
    "南湖",
    "秀洲",
    "宿迁",
    "宿城",
    "宿豫",
    "宿州",
    "大理",
    "苏州",
    "姑苏",
    "虎丘",
    "吴中",
    "相城",
    "吴江",
    "园区",
    "工业园区",
    "常熟",
    "张家港",
    "台州",
    "椒江",
    "黄岩",
    "路桥",
    "玉环",
    "慈溪",
    "玉州",
    "福绵",
    "宜宾",
    "翠屏",
    "南溪",
    "叙州",
    "亳州",
    "衢州",
    "衢江",
    "浔阳",
    "濂溪",
    "柴桑",
    "芜湖",
    "镜湖",
    "鸠江",
    "弋江",
    "湾沚",
    "繁昌",
    "鹤山",
    "兰州",
    "城关",
    "七里河",
    "西固",
    "安宁",
    "绵阳",
    "涪城",
    "游仙",
    "安州",
    "南充",
    "高坪",
    "嘉陵",
    "淄博",
    "淄川",
    "张店",
    "博山",
    "临淄",
    "西安",
    "碑林",
    "莲湖",
    "雁塔",
    "未央",
    "灞桥",
    "长安",
    "揭阳",
    "榕城",
    "揭东",
    "舟山",
    "定海",
    "广州",
    "越秀",
    "海珠",
    "荔湾",
    "天河",
    "白云",
    "黄埔",
    "花都",
    "番禺",
    "南沙",
    "从化",
    "增城",
    "开平",
    "潮州",
    "湘桥",
    "潮安",
    "徐州",
    "云龙",
    "泉山",
    "铜山",
    "贾汪",
    "宜兴",
    "常德",
    "宜昌",
    "拉萨",
    "大连",
    "大连市",
    "西岗",
    "沙河口",
    "甘井子",
    "淮南",
    "东阳",
    "桂林",
    "秀峰",
    "叠彩",
    "象山",
    "七星",
    "雁山",
    "三亚",
    "海棠",
    "吉阳",
    "天涯",
    "崖州",
    "余姚",
    "郴州",
    "清远",
    "福州",
    "临安",
    "邯郸",
    "韶关",
    "镇江",
    "连云港",
    "海州",
    "连云",
    "赣榆",
    "京口",
    "润州",
    "丹徒",
    "张家港",
    "景德",
    "绍兴",
    "越城",
    "柯桥",
    "上虞",
    "诸暨",
    "哈尔滨",
    "南岗",
    "道里",
    "道外",
    "香坊",
    "呼兰",
    "松北",
    "阿城",
    "石家庄",
    "长安",
    "桥西",
    "新华",
    "裕华",
    "乌鲁木齐",
    "西昌",
    "石河子",
    "奉贤",
    "西双版纳",
    "呼和浩特",
    "树木岭",
    "河南岸",
    "仲恺",
    "江夏",
    "附近",
    "小仙女",
    "经开",
    "月亮岛",
    "嫩妹",
    "政府",
    "推荐",
    "万象城",
    "ls",
    "老师",
    "白沙洲",
    "庙山"
  ],
  BOT_TOKENS: [
    {
      id: '7055170525',
      username: '@tiantian_1_bot',
      token: '7055170525:AAGERJzFnnPxgavGRfdGE6d-SaGVgPjbr3o',
    },
    {
      id: '7885577974',
      username: '@tiantian_2_bot',
      token: '7885577974:AAGzDTsAveCNEHUQuTJtfZ4eDb2L-TjTedQ',
    },
    {
      id: '7732107648',
      username: '@tiantian_3_bot',
      token: '7732107648:AAHT84hVGgevj-f9pHADRsGShRYWm3GpvkU',
    },
    {
      id: '7557048184',
      username: '@tiantian_4_bot',
      token: '7557048184:AAHtS8CZx7C6eU5iYdYYRwzJPGd7aEZuyCU',
    },
    {
      id: '8040279101',
      username: '@tiantian_5_bot',
      token: '8040279101:AAHLI-yIW1uSC_a1ax9QKQ6K3hlLEq-sy9c',
    },
    {
      id: '7860826925',
      username: '@tiantian_6_bot',
      token: '7860826925:AAH8oLrGKq1GhWf_SaxzphDjPVVZ3f3BOXI',
    },

    // 测试 bot @fbrotherBot:
    // {
    //   username: '@fbrotherBot',
    //   id: '7051731082',
    //   token: '7051731082:AAEPPuyRi2_uxBV1a2j6LYeYkzBShL1DRBo',
    // },
  ],
  // TARGET_CHAT_ID: '@dandancheng929397',
  TARGET_CHAT_ID: '-1002879906560',
  // 测试用
  // TARGET_CHAT_ID: '-1002465442925',
}
export default CONFIG