{"name": "group-listener", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "tg:login": "tsx src/telegram/login.ts"}, "dependencies": {"@hono/node-server": "^1.13.7", "dotenv": "^16.4.7", "grammy": "^1.36.3", "grammy-middlewares": "^1.0.11", "hono": "^4.6.16", "input": "^1.0.1", "p-queue": "^8.1.0", "telegram": "^2.26.16"}, "devDependencies": {"@types/node": "^20.11.17", "tsx": "^4.7.1"}}