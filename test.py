import asyncio
from telethon.sync import TelegramClient
from telethon.sessions import StringSession

api_id = ********
api_hash = "afe88495d0832f0f6e522ef325e5637f"

async def extract_session():
    try:
        print("🎯 提取session: *************")
        async with TelegramClient('./accounts/*************', api_id, api_hash) as client:
            string_session = StringSession.save(client.session)
            print("✅ 提取成功 Length", len(string_session))
            return string_session
    except Exception as e:
        print(f"客户端提取时发生错误: {str(e)}")
        return f"Error: {str(e)}"

if __name__ == "__main__":
    loop = asyncio.get_event_loop()
    result = loop.run_until_complete(extract_session())
    print("Session Result:", result)