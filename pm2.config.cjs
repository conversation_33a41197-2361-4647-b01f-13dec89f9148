module.exports = {
  apps: [
    {
      name: "group-listener-饿鬼", // 应用名称，可自定义
      script: "pnpm dev", // 运行脚本命令
      // args: "", // 启动命令
      exec_mode: "fork", // 运行模式，可选"cluster"或"fork"
      instances: 1, // 最大实例数，可根据需要自定义
      autorestart: true, // 自动重启
      watch: false, // 监听文件变化，可根据需要自定义
      max_memory_restart: "500M", // 最大内存限制，可根据需要自定义
      cron_restart: "20 17 * * *" // 每天上午 17:20 重启
    },
  ],
};