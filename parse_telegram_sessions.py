from telethon.sync import TelegramClient
from telethon.sessions import StringSession
import os

# 定义 accounts 文件夹路径
accounts_folder = './accounts'

# 获取所有 .session 文件
session_files = [f for f in os.listdir(accounts_folder) if f.endswith('.session')]

# 用于存储 session strings，key 是 phone，value 是 session_string
session_info = {}

# 遍历每个 .session 文件
for session_file in session_files:
    file_path = os.path.join(accounts_folder, session_file)
    
    try:
        phone = session_file.replace('.session', '')
        # 从文件名中提取电话号码（去掉 .session 后缀）

        # 设置 no_updates=True 防止尝试拉取更新
        with TelegramClient(file_path, ********, "afe88495d0832f0f6e522ef325e5637f") as client:
            # 生成 session string
            session_string = StringSession.save(client.session)
            # 存入字典：phone => session_string
            session_info[phone] = session_string
        print(f"Processed: {session_file} | Phone: {phone}")
    except Exception as e:
        print(f"Invalid or expired session: {session_file}, Error: {str(e)}")
        continue

# 输出最终的 session strings 数组
print("All Session Strings:", session_info)


# 1BJWap1sBu1XBHG0u8in8DT4jd1ZgK7t8b_bZ_DAIin67LRminto_ORN4OFujWuw-bDjSLEHh6IZy8rgqjWGnRUnpz2gmqqdiAUeb4lVoPBCvIsAcbd-eqbagcuhLO0DXvR3FhonQoXPUhlEURK2R_fhOiVPpwBi1mzzZ0QxSL9XFk4U9Aye67y8UIVGXazwO95hQkxdvKfoT0FivDicTfkRH6QnsNJLxVZ9F8h9hx-WacTgYQ6HumwXGnrzT6YUN841lMHG8e4-83Czmw5UsJBKIhkn573sYkbnHuacx2R4XGzXQZvRGS7_i-Z1onCjYpvcAbIKe2vmZT7o6-DPcmjw-dq_fsP0=